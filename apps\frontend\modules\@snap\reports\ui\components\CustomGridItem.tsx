import { useState } from "react";
import { GridItem } from "@snap/design-system";
import { useReportMode, useIsTrashEnabled, useIsSaving } from "../../context/ReportContext";
import { LiaTrashRestoreAltSolid, LiaTrashAltSolid } from "react-icons/lia";

interface GridItemProps {
  cols?: 1 | 2 | 3 | 4;
  fullWidth?: boolean;
  children: React.ReactNode;
  className?: string;
}

interface CustomGridItemProps extends GridItemProps {
  onToggleField?: () => void;
  is_selected?: boolean;
  is_deleted?: boolean;
  source?: string[];
  containerClassName?: string;
}

export const CustomGridItem: React.FC<CustomGridItemProps> = ({
  className = "",
  containerClassName = "",
  onToggleField,
  is_selected = false,
  is_deleted = false,
  source = [],
  children,
  cols = 3,
  fullWidth = false,
}) => {
  const mode = useReportMode();
  const isTrashEnabled = useIsTrashEnabled();
  const isSaving = useIsSaving();
  const isPrintMode = mode === "print-pdf";
  const isTrashMode = mode === "trash";
  const [isSelected, setIsSelected] = useState(is_selected);

  const isDisabled = isSaving;

  const handleToggle = () => {
    if (isDisabled) return;

    setIsSelected(!isSelected);
    if (onToggleField) {
      onToggleField();
    }
  };

  const getColSpanClass = () => {
    if (fullWidth) return "col-span-3";

    /* 
    não funciona o breakpoint por conflito tailwind e ds --> `grid grid-cols-1 md:grid-cols-${cols}`
      1: "col-span-3 md:col-span-1",
      2: "col-span-3 md:col-span-2",
      3: "col-span-3",
    */
    return {
      1: "col-span-1",
      2: "col-span-2",
      3: "col-span-3",
      4: "col-span-4",
    }[cols];
  };

  return (
    <div className={`group relative ${containerClassName}`}>
      <div className={`${getColSpanClass()} ${className}`}>{children}</div>
      {
        !isPrintMode && isTrashEnabled && (
          <button
            onClick={handleToggle}
            disabled={isDisabled}
            title={isDisabled ? "Salvando alterações..." : (isTrashMode ? "Restaurar" : "Deletar")}
            className={
              `absolute
              right-4
              top-0
              w-4 h-4
              opacity-0
              group-hover:opacity-100
              cursor-pointer
              disabled:cursor-not-allowed
              transition-opacity duration-200
              ${isDisabled ? 'pointer-events-none' : ''}
              `
            }
          >
            {
              isTrashMode ? (
                <LiaTrashRestoreAltSolid
                  size={32}
                  color={isDisabled ? "var(--muted-foreground)" : "var(--foreground)"}
                />
              ) : (
                <LiaTrashAltSolid
                  size={32}
                  color={isDisabled ? "var(--muted-foreground)" : "var(--primary)"}
                />
              )
            }
          </button>
        )
      }

      {/* TODO - usar para o modo de seleção multipla */}
      {/* <input
        type="checkbox"
        defaultChecked={isSelected}
        disabled={mode === "print-pdf"}
        onChange={handleToggle}
        className="
          absolute
          right-2
          top-2
          transform -translate-y-2

          w-4 h-4
          bg-gray-200
          border border-gray-300
          rounded

          opacity-0
          group-hover:opacity-100
          checked:opacity-100

          cursor-pointer
          disabled:opacity-50

          accent-primary

          transition-opacity duration-200
        "
      /> */}
    </div>
  );
};
