import React from "react";
import { GridContainer } from "@snap/design-system";
import { useReportMode } from "../../context/ReportContext";

interface GridContainerProps {
  children: React.ReactNode;
  gap?: "none" | "sm" | "md" | "lg";
  cols?: 1 | 2 | 3 | 4;
  className?: string;
  columnFirst?: boolean;
}

interface CustomGridContainerProps extends GridContainerProps {
}

export const CustomGridContainer: React.FC<CustomGridContainerProps> = ({
  cols = 3,
  gap = "md",
  children,
  className = "",
  columnFirst = false,
}) => {
  const mode = useReportMode();
  const effectiveCols = mode === "trash" ? 1 : cols;

  const getGapClass = () => {
    switch (gap) {
      case "none":
        return "gap-0";
      case "sm":
        return "gap-3";
      case "md":
        return "gap-4";
      case "lg":
        return "gap-6";
      default:
        return "gap-4";
    }
  };

  const getGridColsClass = () => {
    // Use explicit class mapping instead of template literals to ensure <PERSON><PERSON><PERSON> recognizes the classes
    const gridColsMap = {
      1: "grid grid-cols-1",
      2: "grid grid-cols-2",
      3: "grid grid-cols-3",
      4: "grid grid-cols-4",
    } as const;

    return gridColsMap[effectiveCols as keyof typeof gridColsMap] || "grid grid-cols-3";
  };

  // If columnFirst is false, render normally
  if (!columnFirst) {
    return (
      <div className={`${getGridColsClass()} ${getGapClass()} ${className}`}>
        {children}
      </div>
    );
  }

  // For column-first layout, we need to rearrange the children
  const childrenArray = React.Children.toArray(children);
  const totalItems = childrenArray.length;

  // Calculate the number of rows needed
  const rows = Math.ceil(totalItems / effectiveCols);

  // Create a new 2D array to hold the rearranged children
  const rearrangedChildren: React.ReactNode[][] = Array(rows)
    .fill(null)
    .map(() => Array(effectiveCols).fill(null));

  // Fill the 2D array in column-first order
  childrenArray.forEach((child, index) => {
    const col = Math.floor(index / rows);
    const row = index % rows;

    if (col < effectiveCols) {
      rearrangedChildren[row][col] = child;
    }
  });

  // Flatten the 2D array back to a 1D array
  const flattenedChildren = rearrangedChildren.flat().filter(child => child !== null);

  return (
    <div className={`${getGridColsClass()} ${getGapClass()} ${className}`}>
      {flattenedChildren}
    </div>
  );
};
