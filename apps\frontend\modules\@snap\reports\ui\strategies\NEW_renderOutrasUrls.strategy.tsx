import React from "react";
import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { Url } from "../../model/Urls";
import { GridItem, CustomLabel } from "@snap/design-system";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { translatePropToLabel } from "../../helpers";
import { getFieldLabel, getFieldValue } from "./helpers.strategy";

export function useRenderOutrasUrls(sectionTitle: string): ArrayRenderStrategy<Url> {
  const actions = useReportActions();
  const mode = useReportMode();
  const isTrash = mode === "trash";

  const shouldIncludeBlock = (detalhe: any) => {
    const vals = Object.values(detalhe.value || {});
    return isTrash
      ? vals.some((v: any) => v.is_deleted === true)
      : vals.some((v: any) => v.is_deleted === false);
  };

  const onToggleField = (blockIdx: number, fieldKey: string) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value?.[fieldKey]) {
          detalhe.value[fieldKey].is_deleted = !detalhe.value[fieldKey].is_deleted;

          // Verifica se todos os campos do bloco estão deletados
          const allFieldsDeleted = Object.values(detalhe.value).every((campo: any) =>
            campo.is_deleted === true
          );

          // Se todos os campos estão deletados, marca o bloco como deletado
          // Se nem todos estão deletados, marca o bloco como não deletado
          detalhe.is_deleted = allFieldsDeleted;
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const onToggleBlock = (blockIdx: number) => {
    actions.updateSectionEntries!(
      sectionTitle,
      (entry) => {
        const detalhe = (entry as any).detalhes?.[blockIdx];
        if (detalhe?.value) {
          // Determina o novo estado baseado no modo atual
          const targetDeletedState = isTrash ? false : true;

          // Define o is_deleted do bloco principal
          detalhe.is_deleted = targetDeletedState;

          // Define o is_deleted de todos os campos dentro do bloco
          Object.values(detalhe.value).forEach((campo: any) => {
            if (campo) {
              campo.is_deleted = targetDeletedState;
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  const testEntryDeleted = (entry: any): boolean =>
    entry.detalhes?.every((d: any) =>
      Object.values(d.value).every((v: any) => v.is_deleted === true)
    ) ?? false;

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) &&
    section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // Count non-deleted blocks in detalhes array
      const nonDeletedBlocks = entry.detalhes.filter(
        (bloco: any) => bloco.is_deleted !== true
      ).length;

      return count + nonDeletedBlocks;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (urls?: Url) => React.ReactElement | null
  > = {
    detalhes: (urls) => {
      if (!urls?.detalhes?.length) return null;

      const blocks = urls.detalhes
        .map((d, idx) => ({ bloco: d, idx }))
        .filter(({ bloco }) => shouldIncludeBlock(bloco));

      if (!blocks.length) return null;

      return (
        <CustomGridContainer cols={4}>
          {blocks.map(({ bloco, idx: origIdx }, blockRenderIdx) => (
            <GridItem key={`url-${origIdx}`} cols={1}>
              <CustomGridItem
                cols={1}
                className="pb-4 py-2"
                onToggleField={() => onToggleBlock(origIdx)}
              >
                <CustomLabel
                  label={` URL ${!isTrash ? blockRenderIdx + 1 : ""}`}
                  colorClass="bg-primary"
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                />
              </CustomGridItem>
              <div className="pl-5">
                {Object.entries(bloco.value)
                  .filter(([_, v]: any) =>
                    isTrash ? v.is_deleted : !v.is_deleted
                  )
                  .map(([fieldKey, fieldValue]: any) => (
                    <CustomGridItem
                      key={`url-${origIdx}-${fieldKey}`}
                      cols={1}
                      className="py-2"
                      onToggleField={() => onToggleField(origIdx, fieldKey)}
                    >
                      <CustomReadOnlyInputField
                        label={translatePropToLabel(
                          getFieldLabel(fieldKey, fieldValue)
                        ).toUpperCase()}
                        colorClass="bg-border"
                        icon={
                          <MdOutlineSubdirectoryArrowRight size={16} />
                        }
                        value={String(getFieldValue(fieldValue) || "")}
                        tooltip={renderSourceTooltip(fieldValue.source)}
                      />
                    </CustomGridItem>
                  ))}
              </div>
            </GridItem>
          ))}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof Url>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (urls: Url): React.ReactElement[] => {
    const keys = Object.keys(urls) as Array<keyof Url>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Outras URLs] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(urls))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: Url[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Outras URLs] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && entry.detalhes.some((detalhe: any) =>
          detalhe.value?.url?.is_deleted === true ||
          detalhe.value?.dominio?.is_deleted === true ||
          detalhe.is_deleted === true
        );
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((urls, index) => {
      const elements = renderSingleItem(urls);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`outras-urls-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca todos os campos como deletados/restaurados
        if (entry.detalhes) {
          entry.detalhes.forEach((detalhe: any) => {
            if (detalhe?.value) {
              Object.keys(detalhe.value).forEach(fieldKey => {
                if (detalhe.value[fieldKey]) {
                  detalhe.value[fieldKey].is_deleted = targetDeletedState;
                }
              });
            }
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
