// List of valid image MIME types (expandable as needed)
const VALID_IMAGE_MIME_TYPES = new Set([
  'png', 'jpeg', 'jpg', 'gif', 'bmp', 
  'svg+xml', 'webp', 'apng', 'tiff', 'x-icon'
]);

// Maximum allowed Base64 string length (10MB)
const MAX_BASE64_LENGTH = 10 * 1024 * 1024; 

export const isValidBase64Image = (str: string): boolean => {
  // 1. Basic string and length check
  if (typeof str !== 'string' || str.length > MAX_BASE64_LENGTH) {
    return false;
  }

  // 2. Verify comma separator exists
  const commaIndex = str.indexOf(',');
  if (commaIndex === -1 || commaIndex === 0 || commaIndex === str.length - 1) {
    return false;
  }

  // 3. Extract and validate header
  const header = str.substring(0, commaIndex).trim();
  if (!header.toLowerCase().startsWith('data:image/')) {
    return false;
  }

  // 4. Parse header segments
  const segments = header.split(';').map(seg => seg.trim().toLowerCase());
  
  // 5. Validate encoding (must end with 'base64')
  if (segments[segments.length - 1] !== 'base64') {
    return false;
  }

  // 6. Extract and validate MIME type
  const mimePart = segments[0].split(':')[1] || '';
  const mimeType = mimePart.split('/')[1];
  
  if (!mimeType || !VALID_IMAGE_MIME_TYPES.has(mimeType)) {
    return false;
  }

  // 7. Validate Base64 payload format
  const payload = str.substring(commaIndex + 1);
  console.log("[isValidBase64Image]: payload: ", /^[a-zA-Z0-9+/]+={0,2}$/.test(payload));
  return /^[a-zA-Z0-9+/]+={0,2}$/.test(payload);
};